import type { PageQuery, BaseEntity } from '#/api/common';

export interface WarehouseVO {
  /**
   * 仓库id
   */
  warehouseId: string | number;

  /**
   * 仓库编码
   */
  warehouseNumber: string;

  /**
   * 仓库名称
   */
  warehouseName: string;

  /**
   * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
   */
  warehouseType: string;

  /**
   * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseInventoryStatus: string;

  /**
   * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseRecevingStatus: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 所属部门ID数组
   */
  deptIds?: number[];

  /**
   * 所属部门名称（用于显示）
   */
  deptNames?: string;

}

export interface WarehouseForm extends BaseEntity {
  /**
   * 仓库id
   */
  warehouseId?: string | number;

  /**
   * 仓库编码
   */
  warehouseNumber?: string;

  /**
   * 仓库名称
   */
  warehouseName?: string;

  /**
   * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
   */
  warehouseType?: string;

  /**
   * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseInventoryStatus?: string;

  /**
   * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseRecevingStatus?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 所属部门ID数组
   */
  deptIds?: number[];

  /**
   * 所属部门名称（用于显示）
   */
  deptNames?: string;

}

export interface WarehouseQuery extends PageQuery {
  /**
   * 仓库编码
   */
  warehouseNumber?: string;

  /**
   * 仓库名称
   */
  warehouseName?: string;

  /**
   * 仓库属性（0普通仓库 1车间仓库 2供应商仓库 3客户仓库 4第三方仓储）
   */
  warehouseType?: string;

  /**
   * 库存状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseInventoryStatus?: string;

  /**
   * 收料状态（0可用 1待检 2冻结 3退回冻结 4在途 5收货冻结 6废品 7不良 8不参与核算）
   */
  warehouseRecevingStatus?: string;

  /**
   * 部门ID（用于筛选）
   */
  deptId?: number | string;

  /**
    * 日期范围参数
    */
  params?: any;
}

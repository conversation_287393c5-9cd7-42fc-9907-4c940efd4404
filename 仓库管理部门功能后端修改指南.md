# 仓库管理部门功能后端修改指南

## 📋 需求概述

为仓库管理模块添加部门关联功能，实现：
1. 仓库可以关联多个部门
2. 支持按部门筛选仓库
3. 在仓库列表中显示所属部门名称

## 🎯 前端已实现功能

### 前端字段映射
- **查询筛选**: `deptId` (单个部门ID)
- **表格显示**: `deptNames` (部门名称字符串，如："研发部, 生产部")
- **编辑表单**: `deptIds` (部门ID数组，如：[1, 2, 3])

### API接口调用
- `GET /wms/warehouse/list?deptId=1` - 按部门筛选仓库
- `GET /wms/warehouse/{id}` - 获取仓库详情（需返回deptIds和deptNames）
- `POST /wms/warehouse` - 新增仓库（接收deptIds）
- `PUT /wms/warehouse` - 更新仓库（接收deptIds）

## 🗄️ 数据库设计

### 1. 仓库表 (wms_warehouse)
```sql
-- 现有字段保持不变，无需修改
-- warehouse_id, warehouse_number, warehouse_name, etc.
```

### 2. 新增仓库部门关联表 (wms_warehouse_dept)
```sql
CREATE TABLE wms_warehouse_dept (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    UNIQUE KEY uk_warehouse_dept (warehouse_id, dept_id),
    INDEX idx_warehouse_id (warehouse_id),
    INDEX idx_dept_id (dept_id)
) COMMENT='仓库部门关联表';
```

## 📁 后端代码修改

### 1. 实体类修改

#### WarehouseVo.java
```java
/**
 * 所属部门ID列表
 */
private List<Long> deptIds;

/**
 * 所属部门名称（逗号分隔）
 */
private String deptNames;
```

#### WarehouseBo.java
```java
/**
 * 所属部门ID列表
 */
private List<Long> deptIds;
```

#### WarehouseQueryBo.java
```java
/**
 * 部门ID（用于筛选）
 */
private Long deptId;
```

### 2. 新增关联实体

#### WarehouseDept.java
```java
@Data
@TableName("wms_warehouse_dept")
public class WarehouseDept {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 仓库ID
     */
    private Long warehouseId;
    
    /**
     * 部门ID
     */
    private Long deptId;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
}
```

### 3. Mapper接口修改

#### WarehouseMapper.java
```java
/**
 * 根据部门ID查询仓库列表
 */
@Select("SELECT DISTINCT w.* FROM wms_warehouse w " +
        "LEFT JOIN wms_warehouse_dept wd ON w.warehouse_id = wd.warehouse_id " +
        "WHERE wd.dept_id = #{deptId}")
List<Warehouse> selectByDeptId(@Param("deptId") Long deptId);

/**
 * 查询仓库关联的部门信息
 */
@Select("SELECT wd.dept_id, d.dept_name FROM wms_warehouse_dept wd " +
        "LEFT JOIN sys_dept d ON wd.dept_id = d.dept_id " +
        "WHERE wd.warehouse_id = #{warehouseId}")
List<Map<String, Object>> selectDeptsByWarehouseId(@Param("warehouseId") Long warehouseId);
```

#### WarehouseDeptMapper.java
```java
@Mapper
public interface WarehouseDeptMapper extends BaseMapper<WarehouseDept> {
    
    /**
     * 根据仓库ID删除关联关系
     */
    @Delete("DELETE FROM wms_warehouse_dept WHERE warehouse_id = #{warehouseId}")
    void deleteByWarehouseId(@Param("warehouseId") Long warehouseId);
    
    /**
     * 批量插入仓库部门关联
     */
    void insertBatch(@Param("list") List<WarehouseDept> list);
}
```

### 4. Service层修改

#### IWarehouseService.java
```java
// 现有方法保持不变，无需修改接口
```

#### WarehouseServiceImpl.java
```java
@Autowired
private WarehouseDeptMapper warehouseDeptMapper;

@Override
public TableDataInfo<WarehouseVo> queryPageList(WarehouseQueryBo bo, PageQuery pageQuery) {
    LambdaQueryWrapper<Warehouse> lqw = buildQueryWrapper(bo);
    
    // 如果指定了部门ID，添加部门筛选条件
    if (bo.getDeptId() != null) {
        // 先查询该部门下的所有仓库ID
        List<WarehouseDept> warehouseDepts = warehouseDeptMapper.selectList(
            new LambdaQueryWrapper<WarehouseDept>()
                .eq(WarehouseDept::getDeptId, bo.getDeptId())
        );
        
        if (warehouseDepts.isEmpty()) {
            // 如果该部门下没有仓库，返回空结果
            return TableDataInfo.build(new ArrayList<>());
        }
        
        List<Long> warehouseIds = warehouseDepts.stream()
            .map(WarehouseDept::getWarehouseId)
            .collect(Collectors.toList());
        
        lqw.in(Warehouse::getWarehouseId, warehouseIds);
    }
    
    Page<WarehouseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
    
    // 填充部门信息
    fillDeptInfo(result.getRecords());
    
    return TableDataInfo.build(result);
}

@Override
public WarehouseVo queryById(Long warehouseId) {
    WarehouseVo vo = baseMapper.selectVoById(warehouseId);
    if (vo != null) {
        fillDeptInfo(Collections.singletonList(vo));
    }
    return vo;
}

@Override
@Transactional(rollbackFor = Exception.class)
public Boolean insertByBo(WarehouseBo bo) {
    Warehouse warehouse = MapstructUtils.convert(bo, Warehouse.class);
    boolean flag = baseMapper.insert(warehouse) > 0;
    
    if (flag) {
        bo.setWarehouseId(warehouse.getWarehouseId());
        // 保存部门关联关系
        saveDeptRelations(warehouse.getWarehouseId(), bo.getDeptIds());
    }
    return flag;
}

@Override
@Transactional(rollbackFor = Exception.class)
public Boolean updateByBo(WarehouseBo bo) {
    Warehouse warehouse = MapstructUtils.convert(bo, Warehouse.class);
    boolean flag = baseMapper.updateById(warehouse) > 0;
    
    if (flag) {
        // 更新部门关联关系
        saveDeptRelations(bo.getWarehouseId(), bo.getDeptIds());
    }
    return flag;
}

/**
 * 填充部门信息
 */
private void fillDeptInfo(List<WarehouseVo> list) {
    if (CollUtil.isEmpty(list)) {
        return;
    }
    
    for (WarehouseVo vo : list) {
        List<Map<String, Object>> deptList = baseMapper.selectDeptsByWarehouseId(vo.getWarehouseId());
        
        if (CollUtil.isNotEmpty(deptList)) {
            List<Long> deptIds = deptList.stream()
                .map(map -> Convert.toLong(map.get("dept_id")))
                .collect(Collectors.toList());
            
            String deptNames = deptList.stream()
                .map(map -> Convert.toStr(map.get("dept_name")))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(", "));
            
            vo.setDeptIds(deptIds);
            vo.setDeptNames(deptNames);
        }
    }
}

/**
 * 保存部门关联关系
 */
private void saveDeptRelations(Long warehouseId, List<Long> deptIds) {
    // 先删除原有关联关系
    warehouseDeptMapper.deleteByWarehouseId(warehouseId);
    
    // 插入新的关联关系
    if (CollUtil.isNotEmpty(deptIds)) {
        List<WarehouseDept> relations = deptIds.stream()
            .map(deptId -> {
                WarehouseDept relation = new WarehouseDept();
                relation.setWarehouseId(warehouseId);
                relation.setDeptId(deptId);
                return relation;
            })
            .collect(Collectors.toList());
        
        warehouseDeptMapper.insertBatch(relations);
    }
}
```

## 🔧 XML映射文件修改

### WarehouseDeptMapper.xml
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxx.wms.mapper.WarehouseDeptMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO wms_warehouse_dept (warehouse_id, dept_id, create_time, create_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseId}, #{item.deptId}, NOW(), #{item.createBy})
        </foreach>
    </insert>

</mapper>
```

## ✅ 测试验证

### 1. 数据库测试
```sql
-- 插入测试数据
INSERT INTO wms_warehouse_dept (warehouse_id, dept_id) VALUES (1, 100), (1, 101);

-- 验证查询
SELECT w.*, GROUP_CONCAT(d.dept_name) as dept_names 
FROM wms_warehouse w 
LEFT JOIN wms_warehouse_dept wd ON w.warehouse_id = wd.warehouse_id 
LEFT JOIN sys_dept d ON wd.dept_id = d.dept_id 
WHERE w.warehouse_id = 1;
```

### 2. API测试
```bash
# 按部门筛选
GET /wms/warehouse/list?deptId=100

# 获取仓库详情
GET /wms/warehouse/1

# 创建仓库
POST /wms/warehouse
{
  "warehouseName": "测试仓库",
  "deptIds": [100, 101]
}
```

## 📝 注意事项

1. **事务处理**: 仓库和部门关联的操作需要在同一事务中
2. **数据一致性**: 删除仓库时需要同时删除关联关系
3. **性能优化**: 大量数据时考虑使用批量查询优化部门信息填充
4. **权限控制**: 根据用户部门权限控制可见的仓库范围

## 🚀 部署步骤

1. 执行数据库脚本创建关联表
2. 更新后端代码
3. 重启应用服务
4. 验证前后端联调功能

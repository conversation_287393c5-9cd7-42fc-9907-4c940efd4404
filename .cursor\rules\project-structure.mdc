---
description: 
globs: 
alwaysApply: true
---
# RuoYi-Plus-Vben5 项目结构导航规则

## 项目概述
这是一个基于Vben 5.5.6和Ant Design Vue的企业级后台管理系统前端项目，采用Monorepo架构。

## 核心目录结构

### 主应用
- 主要前端应用位于 [apps/web-antd/](mdc:apps/web-antd) 目录
- 应用入口文件：[apps/web-antd/src/main.ts](mdc:apps/web-antd/src/main.ts)
- 应用启动逻辑：[apps/web-antd/src/bootstrap.ts](mdc:apps/web-antd/src/bootstrap.ts)
- 根组件：[apps/web-antd/src/app.vue](mdc:apps/web-antd/src/app.vue)

### API层
- API请求封装：[apps/web-antd/src/api/request.ts](mdc:apps/web-antd/src/api/request.ts)
- 核心API模块：[apps/web-antd/src/api/core/](mdc:apps/web-antd/src/api/core)
- 系统管理API：[apps/web-antd/src/api/system/](mdc:apps/web-antd/src/api/system)
- 工作流API：[apps/web-antd/src/api/workflow/](mdc:apps/web-antd/src/api/workflow)
- 通用类型定义：[apps/web-antd/src/api/common.d.ts](mdc:apps/web-antd/src/api/common.d.ts)

### 视图层
- 页面视图：[apps/web-antd/src/views/](mdc:apps/web-antd/src/views)
- 系统管理页面：[apps/web-antd/src/views/system/](mdc:apps/web-antd/src/views/system)
- 工作流页面：[apps/web-antd/src/views/workflow/](mdc:apps/web-antd/src/views/workflow)

### 组件层
- 业务组件：[apps/web-antd/src/components/](mdc:apps/web-antd/src/components)
- 数据字典组件：[apps/web-antd/src/components/dict/](mdc:apps/web-antd/src/components/dict)
- 文件上传组件：[apps/web-antd/src/components/upload/](mdc:apps/web-antd/src/components/upload)

### 共享包
- 核心包：[packages/@core/](mdc:packages/@core)
- 状态管理：[packages/stores/](mdc:packages/stores)
- 工具函数：[packages/utils/](mdc:packages/utils)
- 样式包：[packages/styles/](mdc:packages/styles)
- 国际化：[packages/locales/](mdc:packages/locales)

### 配置文件
- 根包配置：[package.json](mdc:package.json)
- 工作空间配置：[pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)
- 构建配置：[turbo.json](mdc:turbo.json)
- Vite配置：[apps/web-antd/vite.config.mts](mdc:apps/web-antd/vite.config.mts)

## 导航原则
1. API相关功能优先查看 `apps/web-antd/src/api/` 目录
2. 页面功能查看 `apps/web-antd/src/views/` 目录
3. 通用组件查看 `apps/web-antd/src/components/` 目录
4. 全局功能查看 `packages/` 目录下的共享包
5. 配置问题查看根目录和 `apps/web-antd/` 下的配置文件

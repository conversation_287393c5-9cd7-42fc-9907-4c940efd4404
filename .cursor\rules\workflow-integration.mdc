---
description: 工作流集成开发模式和最佳实践，包含Warmflow工作流引擎的使用规范。用于工作流相关功能开发和问题排查。
globs: 
alwaysApply: false
---
# 工作流集成和开发规则

## 工作流架构

### warmflow集成
项目集成了warmflow工作流引擎，相关文件：
- 工作流API：[apps/web-antd/src/api/workflow/](mdc:apps/web-antd/src/api/workflow)
- 工作流页面：[apps/web-antd/src/views/workflow/](mdc:apps/web-antd/src/views/workflow)
- 工作流组件：[apps/web-antd/src/views/workflow/components/](mdc:apps/web-antd/src/views/workflow/components)

### 核心模块

#### 流程定义管理
- 流程部署：上传BPMN文件部署流程
- 流程版本管理：支持多版本流程
- 流程分类：按业务分类组织流程

#### 流程实例管理
- 流程启动：根据流程定义启动实例
- 流程监控：实时查看流程执行状态
- 流程图显示：可视化流程执行进度

#### 任务管理
- 待办任务：用户待处理的任务列表
- 已办任务：用户已处理的任务历史
- 任务处理：完成、驳回、转办等操作

### 工作流开发模式

#### 任务处理组件
```vue
<template>
  <div class="task-form">
    <!-- 任务信息展示 -->
    <a-descriptions title="任务信息">
      <a-descriptions-item label="任务名称">
        {{ taskInfo.taskName }}
      </a-descriptions-item>
      <a-descriptions-item label="流程实例">
        {{ taskInfo.processInstanceId }}
      </a-descriptions-item>
    </a-descriptions>
    
    <!-- 业务表单 -->
    <slot name="form" :task="taskInfo" />
    
    <!-- 审批意见 -->
    <a-form-item label="审批意见">
      <a-textarea v-model:value="comment" />
    </a-form-item>
    
    <!-- 操作按钮 -->
    <div class="task-actions">
      <a-button type="primary" @click="handleComplete">
        同意
      </a-button>
      <a-button @click="handleReject">
        驳回
      </a-button>
    </div>
  </div>
</template>
```

#### 流程启动
```typescript
// 启动流程实例
export async function startProcess(data: {
  processKey: string;
  businessKey: string;
  variables?: Record<string, any>;
}) {
  return requestClient.post('/workflow/processInstance/start', data);
}

// 使用示例
const handleStartProcess = async () => {
  await startProcess({
    processKey: 'leave_process',
    businessKey: 'leave_001',
    variables: {
      userId: userInfo.userId,
      deptId: userInfo.deptId,
    },
  });
};
```

#### 任务处理
```typescript
// 完成任务
export async function completeTask(data: {
  taskId: string;
  variables?: Record<string, any>;
  comment?: string;
}) {
  return requestClient.post(`/workflow/task/complete/${data.taskId}`, data);
}

// 驳回任务
export async function rejectTask(data: {
  taskId: string;
  comment: string;
}) {
  return requestClient.post(`/workflow/task/reject/${data.taskId}`, data);
}
```

### 业务集成模式

#### 请假流程示例
参考 [apps/web-antd/src/views/workflow/leave/](mdc:apps/web-antd/src/views/workflow/leave) 目录：
- 请假申请表单
- 请假审批流程
- 请假记录查询

#### 流程变量设置
```typescript
// 设置流程变量
const processVariables = {
  // 申请人信息
  applicant: userInfo.userName,
  applicantId: userInfo.userId,
  deptId: userInfo.deptId,
  
  // 业务数据
  leaveType: formData.leaveType,
  leaveDays: formData.leaveDays,
  startDate: formData.startDate,
  endDate: formData.endDate,
  
  // 审批配置
  needManagerApproval: formData.leaveDays > 3,
  needHrApproval: formData.leaveDays > 7,
};
```

### 流程监控

#### 流程图显示
```vue
<template>
  <div class="process-diagram">
    <iframe
      :src="diagramUrl"
      style="width: 100%; height: 600px; border: none;"
    />
  </div>
</template>

<script setup lang="ts">
const diagramUrl = computed(() => {
  return `/workflow/processInstance/diagram/${processInstanceId}`;
});
</script>
```

#### 流程历史
```typescript
// 获取流程历史
export async function getProcessHistory(processInstanceId: string) {
  return requestClient.get(`/workflow/processInstance/history/${processInstanceId}`);
}
```

### 工作流最佳实践

#### 表单设计
- 业务表单与流程表单分离
- 使用流程变量传递关键业务数据
- 支持表单数据回显和编辑控制

#### 权限控制
- 任务办理人权限验证
- 流程启动权限控制
- 流程监控权限管理

#### 异常处理
- 流程异常监控和告警
- 超时任务自动处理
- 流程回退和撤销机制

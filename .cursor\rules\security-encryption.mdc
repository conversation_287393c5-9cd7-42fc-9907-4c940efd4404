---
description: 
globs: apps/web-antd/src/utils/auth.ts,apps/web-antd/src/utils/cipher.ts,apps/web-antd/src/api/request.ts,packages/utils/src/auth/**/*.ts,packages/stores/src/auth.ts
alwaysApply: false
---
# 安全和加密处理规则

## 加密机制

### 双重加密架构
项目使用RSA+AES双重加密机制，实现在：
- AES加密工具：[apps/web-antd/src/utils/encryption/crypto.ts](mdc:apps/web-antd/src/utils/encryption/crypto.ts)
- RSA加密工具：[apps/web-antd/src/utils/encryption/jsencrypt.ts](mdc:apps/web-antd/src/utils/encryption/jsencrypt.ts)

### 加密流程
1. 客户端生成AES密钥
2. 使用RSA公钥加密AES密钥，放入请求头 `encrypt-key`
3. 使用AES密钥加密请求数据
4. 服务端返回时，使用RSA私钥解密响应头中的AES密钥
5. 使用AES密钥解密响应数据

### 请求加密配置
在 [apps/web-antd/src/api/request.ts](mdc:apps/web-antd/src/api/request.ts) 中配置：
```typescript
// 启用加密的请求
requestClient.post('/api/endpoint', data, {
  encrypt: true
});
```

### 环境变量配置
加密相关的环境变量：
- `VITE_GLOB_ENABLE_ENCRYPT`: 全局加密开关
- `VITE_GLOB_RSA_PUBLIC_KEY`: RSA公钥（请求加密）
- `VITE_GLOB_RSA_PRIVATE_KEY`: RSA私钥（响应解密）

## 权限控制

### RBAC权限模型
- 用户 → 角色 → 权限的三层权限模型
- 数据权限支持：全部、自定义、部门、部门及下级、仅本人

### 权限检查
- 页面级权限：通过路由守卫 [apps/web-antd/src/router/guard.ts](mdc:apps/web-antd/src/router/guard.ts)
- 组件级权限：使用 `v-access` 指令
- API级权限：在请求拦截器中检查

### 权限相关文件
- 权限控制逻辑：[apps/web-antd/src/router/access.ts](mdc:apps/web-antd/src/router/access.ts)
- 认证API：[apps/web-antd/src/api/core/auth.ts](mdc:apps/web-antd/src/api/core/auth.ts)

## Token管理

### Token存储
- 使用 `@vben/stores` 包中的认证store管理token
- 支持访问令牌和刷新令牌机制

### 自动登出
- 401响应自动触发登出流程
- 防止重复登出的标志位控制

## 多租户安全

### 租户隔离
- 请求头自动添加 `Tenant-Id`
- 租户管理API：[apps/web-antd/src/api/system/tenant/](mdc:apps/web-antd/src/api/system/tenant)

### 租户切换
- 切换租户时重新获取用户信息和权限
- 数据自动隔离，防止跨租户访问

## 安全最佳实践

### 密码处理
- 登录和重置密码等敏感操作必须加密传输
- 使用 `encrypt: true` 选项启用加密

### 数据验证
- 前端表单验证使用 vee-validate + zod
- 关键操作需要二次确认

### 日志和监控
- 重要操作进行日志记录
- 异常情况自动上报
